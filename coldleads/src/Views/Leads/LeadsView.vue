<template>
  <div class="flex h-screen">
    <div class="hidden lg:flex">
      <Sidebar
        :active-item="currentPage"
        :lead-count="leadCount"
        :user-name="userName"
        :user-email="userEmail"
        :is-mobile-menu-open="false"
        @navigate="handleNavigation"
        @logout="handleLogout"
      />
    </div>

    <div
      v-if="isMobileMenuOpen"
      class="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50"
    >
      <div class="absolute left-0 top-0 h-full w-64 bg-white shadow-lg">
        <Sidebar
          :active-item="currentPage"
          :lead-count="leadCount"
          :user-name="userName"
          :user-email="userEmail"
          :is-mobile-menu-open="isMobileMenuOpen"
          @navigate="handleNavigation"
          @logout="handleLogout"
          @toggle-mobile-menu="toggleMobileMenu"
        />
      </div>
    </div>

    <!-- Main content -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Header -->
      <header class="bg-white border-b border-neutral-200 px-4 lg:px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- Mobile Menu Button -->
            <button
              @click="toggleMobileMenu"
              class="lg:hidden text-neutral-500 hover:text-neutral-700 p-2 -ml-2"
            >
              <svg
                class="w-6 h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
            </button>

            <!-- Page Title -->
            <h2
              class="text-xl lg:text-2xl font-bold text-neutral-900 capitalize"
            >
              Leads
            </h2>
          </div>

          <!-- Header Actions -->
          <div class="flex items-center space-x-2 lg:space-x-4">
            <button
              class="bg-emerald-500 hover:bg-emerald-600 text-white px-3 py-2 lg:px-4 rounded-lg text-sm font-medium transition-colors"
            >
              <span class="hidden sm:inline">Add New Lead</span>
              <span class="sm:hidden">Add</span>
            </button>

            <!-- Notifications -->
            <button
              class="relative p-2 text-neutral-400 hover:text-neutral-600 transition-colors"
            >
              <svg
                class="w-5 h-5 lg:w-6 lg:h-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 17h5l-5-5-5 5h5zm0 0v-3a3 3 0 00-3-3H9a3 3 0 00-3 3v3"
                />
              </svg>
              <span
                class="absolute top-1 right-1 w-2 h-2 bg-emerald-500 rounded-full"
              ></span>
            </button>
          </div>
        </div>
      </header>

      <main class="flex-1 overflow-auto p-4 lg:p-6">
        <LeadsListComponent :leads="leads" />
      </main>
    </div>
  </div>
</template>

<script setup lang="js">
import { ref, onMounted } from 'vue'
import Sidebar from '../../components/SideBarComponent/SideBarComponent.vue'
import { useRouter } from 'vue-router'
import { logout } from '../../Composable/useAuth'
import LeadsListComponent from '../../components/LeadsListComponent/LeadsListComponent.vue'
import axios from 'axios';
import { ENDPOINTS } from '../../Config/api';

const router = useRouter()

const currentPage = ref('dashboard')
const isMobileMenuOpen = ref(false)

const handleNavigation = (page) => {
  currentPage.value = page
  console.log('Current page:', currentPage.value);
  console.log('Navigating to:', page)
  router.push(`/${page}`)
}

const handleLogout = () => {
  console.log('Logging out from dashboard...')
  logout()
}

const leadCount = ref(100)
const userName = ref('John Doe')
const userEmail = ref('<EMAIL>')

const toggleMobileMenu = (forceState = null) => {
  if (forceState !== null) {
    isMobileMenuOpen.value = forceState
  } else {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
  }
}

const leads = ref([])




async function fetchLeads() {
  try {
    const response = await axios.get(ENDPOINTS.LEADS, {
      params: {
          user_id: 1
      }
    })
    leads.value = response.data
  } catch (error) {
    console.error('Error fetching leads:', error)
  }
}
</script>
