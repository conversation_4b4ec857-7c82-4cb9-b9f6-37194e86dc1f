<template>
  <div
    class="min-h-screen bg-white flex items-center justify-center px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <!-- <PERSON><PERSON> and Header -->
      <div class="text-center">
        <div class="flex justify-center mb-6">
          <div
            class="w-16 h-16 bg-emerald-500 rounded-xl flex items-center justify-center"
          >
            <svg
              class="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
        </div>
        <h1 class="text-3xl font-bold text-neutral-900 mb-2">ColdLeads</h1>
        <p class="text-neutral-600">Sign in to your account</p>
      </div>

      <!-- Login Form -->
      <form @submit.prevent="handleLogin" class="mt-8 space-y-6">
        <div class="space-y-5">
          <!-- Email Field -->
          <div>
            <label
              for="email"
              class="block text-sm font-medium text-neutral-700 mb-2"
            >
              Email address
            </label>
            <input
              id="email"
              v-model="email"
              type="email"
              required
              class="w-full px-4 py-3 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors placeholder-neutral-400 text-neutral-900"
              placeholder="Enter your email"
            />
          </div>

          <!-- Password Field -->
          <div>
            <label
              for="password"
              class="block text-sm font-medium text-neutral-700 mb-2"
            >
              Password
            </label>
            <div class="relative">
              <input
                id="password"
                v-model="password"
                :type="showPassword ? 'text' : 'password'"
                required
                class="w-full px-4 py-3 pr-12 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition-colors placeholder-neutral-400 text-neutral-900"
                placeholder="Enter your password"
              />
              <button
                type="button"
                @click="togglePassword"
                class="absolute inset-y-0 right-0 flex items-center pr-4 text-neutral-400 hover:text-neutral-600"
              >
                <svg
                  v-if="showPassword"
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"
                  />
                </svg>
                <svg
                  v-else
                  class="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Remember Me and Forgot Password -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="remember-me"
              v-model="rememberMe"
              type="checkbox"
              class="h-4 w-4 text-emerald-500 focus:ring-emerald-500 border-neutral-300 rounded"
            />
            <label for="remember-me" class="ml-2 text-sm text-neutral-700">
              Remember me
            </label>
          </div>
          <a
            href="#"
            class="text-sm text-emerald-600 hover:text-emerald-700 font-medium"
          >
            Forgot password?
          </a>
        </div>

        <!-- Submit Button -->
        <button
          type="submit"
          :disabled="loading"
          class="w-full flex justify-center py-3 px-4 border border-transparent rounded-lg shadow-sm text-sm font-medium text-white bg-emerald-500 hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <svg
            v-if="loading"
            class="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              class="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="4"
            ></circle>
            <path
              class="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            ></path>
          </svg>
          {{ loading ? "Signing in..." : "Sign in" }}
        </button>

        <!-- Sign Up Link -->
        <div class="text-center">
          <p class="text-sm text-neutral-600">
            Don't have an account?
            <a
              href="#"
              class="font-medium text-emerald-600 hover:text-emerald-700"
            >
              Sign up for free
            </a>
          </p>
        </div>
      </form>

      <div class="text-center text-xs text-neutral-400 mt-8">
        © 2025 ColdLeads. All rights reserved.
      </div>
    </div>
  </div>
</template>

<script setup lang="js">
import { ref } from 'vue'
import { colors } from '../../Composable/color'
import axios from 'axios';
import { useToast } from 'vue-toastification';
import { ENDPOINTS } from '../../Config/api';
import { useRouter } from 'vue-router'

const router = useRouter();
const toast = useToast();
// Reactive data
const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const showPassword = ref(false)
const loading = ref(false)

// Methods
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

const handleLogin = async () => {
  if (!email || !password) {
    toast.error('Please fill in all fields');
    return;
  }

  loading.value = true;
  try {
    const response = await axios.post(ENDPOINTS.login, {
      email: email.value,
      password: password.value
    });
    if (response.status === 200) {
      const { token, data: user } = response.data;
      localStorage.setItem("token", token);
      localStorage.setItem("user", JSON.stringify(user));
      toast.success('Login successful');
      email.value = "";
      password.value = "";
      router.push("/dashboard");
    } else {
      console.log(response);
      toast.error("Login failed");
    }
  } catch (error) {
    console.log(error);
    toast.error("Internal server error");
  } finally {
    loading.value = false;
  }
}
</script>
