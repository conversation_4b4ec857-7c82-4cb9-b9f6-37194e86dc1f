<template>
  <div class="flex h-screen bg-neutral-50 overflow-hidden">
    <Sidebar 
      :active-item="currentPage"
      :lead-count="leadCount"
      :user-name="userName"
      :user-email="userEmail"
      :is-mobile-menu-open="isMobileMenuOpen"
      @navigate="handleNavigation"
      @logout="handleLogout"
      @toggle-mobile-menu="toggleMobileMenu"
    />
    
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- Header with Mobile Menu Button -->
      <header class="bg-white border-b border-neutral-200 px-4 lg:px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <button
              @click="toggleMobileMenu"
              class="lg:hidden text-neutral-500 hover:text-neutral-700 p-2 -ml-2"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <!-- Page Title -->
            <h2 class="text-xl lg:text-2xl font-bold text-neutral-900 capitalize">{{ currentPage }}</h2>
          </div>
          
          <!-- Header Actions -->
          <div class="flex items-center space-x-2 lg:space-x-4">
            <button class="bg-emerald-500 hover:bg-emerald-600 text-white px-3 py-2 lg:px-4 rounded-lg text-sm font-medium transition-colors">
              <span class="hidden sm:inline">Add New Lead</span>
              <span class="sm:hidden">Add</span>
            </button>
            
            <!-- Notifications -->
            <button class="relative p-2 text-neutral-400 hover:text-neutral-600 transition-colors">
              <svg class="w-5 h-5 lg:w-6 lg:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5-5-5 5h5zm0 0v-3a3 3 0 00-3-3H9a3 3 0 00-3 3v3" />
              </svg>
              <span class="absolute top-1 right-1 w-2 h-2 bg-emerald-500 rounded-full"></span>
            </button>
          </div>
        </div>
      </header>
      
      <!-- Main Content -->
      <main class="flex-1 overflow-auto p-4 lg:p-6">
        <!-- Dashboard Content -->
        <div v-if="currentPage === 'dashboard'">
          <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-6 lg:mb-8">
            <!-- Stats Cards -->
            <div class="bg-white p-4 lg:p-6 rounded-xl border border-neutral-200">
              <div class="flex items-center">
                <div class="p-2 bg-emerald-50 rounded-lg">
                  <svg class="w-5 h-5 lg:w-6 lg:h-6 text-emerald-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                <div class="ml-3 lg:ml-4">
                  <p class="text-xs lg:text-sm font-medium text-neutral-600">Total Leads</p>
                  <p class="text-lg lg:text-2xl font-bold text-neutral-900">1,234</p>
                </div>
              </div>
            </div>
            
            <!-- Active Campaigns Card -->
            <div class="bg-white p-4 lg:p-6 rounded-xl border border-neutral-200">
              <div class="flex items-center">
                <div class="p-2 bg-purple-50 rounded-lg">
                  <svg class="w-5 h-5 lg:w-6 lg:h-6 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                  </svg>
                </div>
                <div class="ml-3 lg:ml-4">
                  <p class="text-xs lg:text-sm font-medium text-neutral-600">Active Campaigns</p>
                  <p class="text-lg lg:text-2xl font-bold text-neutral-900">12</p>
                </div>
              </div>
            </div>

            <!-- Conversion Rate Card -->
            <div class="bg-white p-4 lg:p-6 rounded-xl border border-neutral-200">
              <div class="flex items-center">
                <div class="p-2 bg-blue-50 rounded-lg">
                  <svg class="w-5 h-5 lg:w-6 lg:h-6 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div class="ml-3 lg:ml-4">
                  <p class="text-xs lg:text-sm font-medium text-neutral-600">Conversion Rate</p>
                  <p class="text-lg lg:text-2xl font-bold text-neutral-900">24.5%</p>
                </div>
              </div>
            </div>

            <!-- Revenue Card -->
            <div class="bg-white p-4 lg:p-6 rounded-xl border border-neutral-200">
              <div class="flex items-center">
                <div class="p-2 bg-green-50 rounded-lg">
                  <svg class="w-5 h-5 lg:w-6 lg:h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div class="ml-3 lg:ml-4">
                  <p class="text-xs lg:text-sm font-medium text-neutral-600">Revenue</p>
                  <p class="text-lg lg:text-2xl font-bold text-neutral-900">$45.2K</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Recent Activity -->
          <div class="bg-white rounded-xl border border-neutral-200 p-4 lg:p-6">
            <h3 class="text-lg font-semibold text-neutral-900 mb-4">Recent Activity</h3>
            <div class="space-y-4">
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-emerald-500 rounded-full"></div>
                <p class="text-sm text-neutral-600">New lead added from Campaign #1</p>
                <span class="text-xs text-neutral-400 ml-auto">2 min ago</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-purple-500 rounded-full"></div>
                <p class="text-sm text-neutral-600">Campaign "Summer Sale" completed</p>
                <span class="text-xs text-neutral-400 ml-auto">1 hour ago</span>
              </div>
              <div class="flex items-center space-x-3">
                <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                <p class="text-sm text-neutral-600">Analytics report generated</p>
                <span class="text-xs text-neutral-400 ml-auto">3 hours ago</span>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="js">
import { ref } from 'vue'
import Sidebar from '../../components/SideBarComponent/SideBarComponent.vue'
import { useRouter } from 'vue-router'
import { logout } from '../../Composable/useAuth'
const router = useRouter()

const currentPage = ref('dashboard')
const isMobileMenuOpen = ref(false)

const handleNavigation = (page) => {
  currentPage.value = page
  console.log('Navigating to:', page)  
  router.push(`/${page}`)
}

const handleLogout = () => {
  console.log('Logging out from dashboard...')
  logout()
}

const leadCount = ref(100)
const userName = ref('John Doe')
const userEmail = ref('<EMAIL>')

const toggleMobileMenu = (forceState = null) => {
  if (forceState !== null) {
    isMobileMenuOpen.value = forceState
  } else {
    isMobileMenuOpen.value = !isMobileMenuOpen.value
  }
}

</script>