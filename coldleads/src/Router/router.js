import { createRouter, createWebHashHistory } from "vue-router";
import LoginView from "../Views/Auth/LoginView.vue";
import DashboardView from "../Views/Dashboard/DashBoardView.vue";
import LeadsView from "../Views/Leads/LeadsView.vue";
import { isLoggedIn } from "../Composable/useAuth";

const routes = [
  {
    path: "/",
    component: LoginView,
  },
  {
    path: "/dashboard",
    component: DashboardView,
    meta: { requiresAuth: true },
  },
  {
    path: "/leads",
    component: LeadsView,
    meta: { requiresAuth: true }, 
  },
];

const router = createRouter({
  history: createWebHashHistory(),
  routes,
});

// // Global navigation guard
// router.beforeEach((to, from, next) => {
//   if (to.meta.requiresAuth && !isLoggedIn()) {
//     next("/"); 
//   } else if (to.path === "/" && isLoggedIn()) {
//     next("/dashboard");
//   } else {
//     next();
//   }
// });

export default router;
