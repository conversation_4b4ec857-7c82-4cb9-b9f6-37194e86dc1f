<template>
  <div class="flex-1 flex flex-col overflow-hidden bg-neutral-50">
    <!-- Filters Section -->
    <div class="bg-white border-b border-neutral-200 p-4 lg:p-6">
      <div class="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
        <h1 class="text-2xl font-bold text-neutral-900">Leads</h1>
        
        <!-- Filter Controls -->
        <div class="flex flex-col sm:flex-row gap-3">
          <!-- Search Input -->
          <div class="relative">
            <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-neutral-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search leads..."
              class="pl-10 pr-4 py-2 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm w-full sm:w-64"
            />
          </div>

          <!-- Niche Filter -->
          <select
            v-model="selectedNiche"
            class="px-3 py-2 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          >
            <option value="">All Niches</option>
            <option v-for="niche in uniqueNiches" :key="niche" :value="niche">
              {{ niche }}
            </option>
          </select>

          <!-- Sort Filter -->
          <select
            v-model="sortBy"
            class="px-3 py-2 border border-neutral-200 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 text-sm"
          >
            <option value="name">Sort by Name</option>
            <option value="email">Sort by Email</option>
            <option value="niche">Sort by Niche</option>
            <option value="website">Sort by Website</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Results Summary -->
    <div class="bg-white border-b border-neutral-100 px-4 lg:px-6 py-3">
      <div class="flex items-center justify-between">
        <p class="text-sm text-neutral-600">
          Showing {{ filteredLeads.length }} of {{ leads.length }} leads
        </p>
        <div class="flex items-center space-x-2">
          <button 
            @click="viewMode = 'list'"
            :class="[
              'p-2 rounded-lg transition-colors',
              viewMode === 'list' ? 'bg-emerald-100 text-emerald-600' : 'text-neutral-400 hover:text-neutral-600'
            ]"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
            </svg>
          </button>
          <button 
            @click="viewMode = 'grid'"
            :class="[
              'p-2 rounded-lg transition-colors',
              viewMode === 'grid' ? 'bg-emerald-100 text-emerald-600' : 'text-neutral-400 hover:text-neutral-600'
            ]"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM14 5a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1V5zM4 15a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1H5a1 1 0 01-1-1v-4zM14 15a1 1 0 011-1h4a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 01-1-1v-4z" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Leads List/Grid -->
    <div class="flex-1 overflow-auto p-4 lg:p-6">
      <!-- List View -->
      <div v-if="viewMode === 'list'" class="bg-white rounded-xl border border-neutral-200 overflow-hidden">
        <!-- Table Header -->
        <div class="bg-neutral-50 border-b border-neutral-200 px-6 py-4">
          <div class="grid grid-cols-12 gap-4 text-sm font-medium text-neutral-700">
            <div class="col-span-3">Name & Email</div>
            <div class="col-span-2">Niche</div>
            <div class="col-span-3">Website</div>
            <div class="col-span-2">LinkedIn</div>
            <div class="col-span-2">Actions</div>
          </div>
        </div>

        <!-- Table Body -->
        <div class="divide-y divide-neutral-100">
          <div 
            v-for="lead in filteredLeads" 
            :key="lead.id"
            class="px-6 py-4 hover:bg-neutral-50 transition-colors"
          >
            <div class="grid grid-cols-12 gap-4 items-center">
              <!-- Name & Email -->
              <div class="col-span-3">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                    <span class="text-sm font-medium text-emerald-700">
                      {{ lead.name.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                  <div>
                    <p class="font-medium text-neutral-900">{{ lead.name }}</p>
                    <p class="text-sm text-neutral-500">{{ lead.email }}</p>
                  </div>
                </div>
              </div>

              <!-- Niche -->
              <div class="col-span-2">
                <span class="inline-flex px-2 py-1 text-xs font-medium bg-purple-100 text-purple-700 rounded-full">
                  {{ lead.niche }}
                </span>
              </div>

              <!-- Website -->
              <div class="col-span-3">
                <a 
                  :href="lead.website" 
                  target="_blank" 
                  class="text-emerald-600 hover:text-emerald-700 text-sm font-medium truncate block"
                >
                  {{ lead.website }}
                </a>
              </div>

              <!-- LinkedIn -->
              <div class="col-span-2">
                <a 
                  :href="lead.linkedinUrl" 
                  target="_blank" 
                  class="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm"
                >
                  <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                  Profile
                </a>
              </div>

              <!-- Actions -->
              <div class="col-span-2">
                <div class="flex items-center space-x-2">
                  <button 
                    @click="$emit('edit-lead', lead)"
                    class="text-neutral-400 hover:text-emerald-600 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                  </button>
                  <button 
                    @click="$emit('delete-lead', lead.id)"
                    class="text-neutral-400 hover:text-red-600 transition-colors"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Grid View -->
      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <div 
          v-for="lead in filteredLeads" 
          :key="lead.id"
          class="bg-white rounded-xl border border-neutral-200 p-6 hover:shadow-md transition-shadow"
        >
          <!-- Header -->
          <div class="flex items-center justify-between mb-4">
            <div class="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center">
              <span class="text-lg font-medium text-emerald-700">
                {{ lead.name.charAt(0).toUpperCase() }}
              </span>
            </div>
            <div class="flex space-x-2">
              <button 
                @click="$emit('edit-lead', lead)"
                class="text-neutral-400 hover:text-emerald-600 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
              </button>
              <button 
                @click="$emit('delete-lead', lead.id)"
                class="text-neutral-400 hover:text-red-600 transition-colors"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          </div>

          <!-- Lead Info -->
          <div class="space-y-3">
            <div>
              <h3 class="font-semibold text-neutral-900">{{ lead.name }}</h3>
              <p class="text-sm text-neutral-500">{{ lead.email }}</p>
            </div>

            <div>
              <span class="inline-flex px-2 py-1 text-xs font-medium bg-purple-100 text-purple-700 rounded-full">
                {{ lead.niche }}
              </span>
            </div>

            <div class="space-y-2">
              <a 
                :href="lead.website" 
                target="_blank" 
                class="flex items-center text-emerald-600 hover:text-emerald-700 text-sm font-medium"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                </svg>
                Visit Website
              </a>

              <a 
                :href="lead.linkedinUrl" 
                target="_blank" 
                class="flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
                LinkedIn Profile
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- Empty State -->
      <div v-if="filteredLeads.length === 0" class="text-center py-12">
        <svg class="w-12 h-12 text-neutral-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
        <h3 class="text-lg font-medium text-neutral-900 mb-2">No leads found</h3>
        <p class="text-neutral-500">Try adjusting your search or filters to find what you're looking for.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// Define props
const props = defineProps({
  leads: {
    type: Array,
    default: () => []
  }
})

// Define emits
const emit = defineEmits(['add-lead', 'edit-lead', 'delete-lead'])

// Sample data (fallback if no props provided)
const defaultLeads = [
  {
    id: 1,
    name: 'John Smith',
    email: '<EMAIL>',
    niche: 'Technology',
    website: 'https://techstartup.com',
    linkedinUrl: 'https://linkedin.com/in/johnsmith'
  },
  {
    id: 2,
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    niche: 'Fashion',
    website: 'https://fashionbrand.com',
    linkedinUrl: 'https://linkedin.com/in/sarahjohnson'
  },
  {
    id: 3,
    name: 'Michael Chen',
    email: '<EMAIL>',
    niche: 'Healthcare',
    website: 'https://healthtech.io',
    linkedinUrl: 'https://linkedin.com/in/michaelchen'
  },
  {
    id: 4,
    name: 'Emma Davis',
    email: '<EMAIL>',
    niche: 'Food & Beverage',
    website: 'https://fooddelivery.com',
    linkedinUrl: 'https://linkedin.com/in/emmadavis'
  },
  {
    id: 5,
    name: 'David Wilson',
    email: '<EMAIL>',
    niche: 'Finance',
    website: 'https://financeapp.com',
    linkedinUrl: 'https://linkedin.com/in/davidwilson'
  },
  {
    id: 6,
    name: 'Lisa Rodriguez',
    email: '<EMAIL>',
    niche: 'Education',
    website: 'https://eduplatform.com',
    linkedinUrl: 'https://linkedin.com/in/lisarodriguez'
  },
  {
    id: 7,
    name: 'James Brown',
    email: '<EMAIL>',
    niche: 'Real Estate',
    website: 'https://realestatetech.com',
    linkedinUrl: 'https://linkedin.com/in/jamesbrown'
  },
  {
    id: 8,
    name: 'Amanda Taylor',
    email: '<EMAIL>',
    niche: 'Travel',
    website: 'https://travelapp.com',
    linkedinUrl: 'https://linkedin.com/in/amandataylor'
  }
]

// Use props.leads if provided, otherwise use default data
const leadsData = computed(() => props.leads.length > 0 ? props.leads : defaultLeads)

// Reactive filters
const searchQuery = ref('')
const selectedNiche = ref('')
const sortBy = ref('name')
const viewMode = ref('list')

// Computed properties
const uniqueNiches = computed(() => {
  return [...new Set(leadsData.value.map(lead => lead.niche))].sort()
})

const filteredLeads = computed(() => {
  let filtered = leadsData.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(lead =>
      lead.name.toLowerCase().includes(query) ||
      lead.email.toLowerCase().includes(query) ||
      lead.niche.toLowerCase().includes(query) ||
      lead.website.toLowerCase().includes(query)
    )
  }

  // Apply niche filter
  if (selectedNiche.value) {
    filtered = filtered.filter(lead => lead.niche === selectedNiche.value)
  }

  // Apply sorting
  filtered.sort((a, b) => {
    const aValue = a[sortBy.value].toLowerCase()
    const bValue = b[sortBy.value].toLowerCase()
    return aValue.localeCompare(bValue)
  })

  return filtered
})

// Expose leads count for parent component
defineExpose({
  leadCount: computed(() => leadsData.value.length)
})
</script>