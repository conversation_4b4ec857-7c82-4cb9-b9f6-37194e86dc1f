<template>
  <!-- Mobile Overlay -->
  <div
    v-if="isMobileMenuOpen"
    class="fixed inset-0 z-40 lg:hidden"
    @click="closeMobileMenu"
  ></div>

  <div
    :class="[
      'h-screen bg-white border-r border-neutral-200 flex flex-col transition-transform duration-300 ease-in-out z-50',
      // Desktop
      'lg:relative lg:translate-x-0 lg:w-84',
      // Mobile
      'fixed lg:static',
      isMobileMenuOpen
        ? 'translate-x-0 w-64'
        : '-translate-x-full w-64 lg:w-64 lg:translate-x-0',
    ]"
  >
    <div class="flex items-center px-6 py-6 border-b border-neutral-100">
      <div class="flex items-center space-x-3 flex-1">
        <div
          class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"
        >
          <svg
            class="w-5 h-5 text-white"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M13 10V3L4 14h7v7l9-11h-7z"
            />
          </svg>
        </div>
        <h1 class="text-xl font-bold text-neutral-900">ColdLeads</h1>
      </div>

      <button
        @click="closeMobileMenu"
        class="lg:hidden text-neutral-500 hover:text-neutral-700 p-1"
      >
        <svg
          class="w-6 h-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>
    </div>

    <!-- Sidebar Navigation -->
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
      <router-link
        v-for="item in menuItems"
        :key="item.name"
        :to="item.to"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
        :class="
          isActive(item.to)
            ? 'bg-emerald-50 text-emerald-700 border border-emerald-200'
            : 'text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900'
        "
        @click="closeMobileMenu"
      >
        <component :is="item.icon" class="w-5 h-5 mr-3 flex-shrink-0" />
        <span class="flex-1">{{ item.label }}</span>
        <span
          v-if="item.name === 'leads' && leadCount > 0"
          class="bg-emerald-100 text-emerald-700 text-xs px-2 py-1 rounded-full ml-2"
        >
          {{ leadCount }}
        </span>
      </router-link>

      <!-- Divider -->
      <div class="border-t border-neutral-100 my-4"></div>

      <!-- Bottom items -->
      <router-link
        v-for="item in bottomItems"
        :key="item.name"
        :to="item.to"
        class="flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-colors"
        :class="
          isActive(item.to)
            ? 'bg-emerald-50 text-emerald-700 border border-emerald-200'
            : 'text-neutral-600 hover:bg-neutral-50 hover:text-neutral-900'
        "
        @click="closeMobileMenu"
      >
        <component :is="item.icon" class="w-5 h-5 mr-3 flex-shrink-0" />
        {{ item.label }}
      </router-link>
    </nav>

    <!-- User Profile Section -->
    <div class="border-t border-neutral-100 p-4">
      <div class="flex items-center space-x-3">
        <div
          class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center flex-shrink-0"
        >
          <span class="text-sm font-medium text-white">{{ userInitials }}</span>
        </div>
        <div class="flex-1 min-w-0">
          <p class="text-sm font-medium text-neutral-900 truncate">
            {{ userName }}
          </p>
          <p class="text-xs text-neutral-500 truncate">{{ userEmail }}</p>
        </div>
        <button
          @click="handleLogout"
          class="text-neutral-400 hover:text-neutral-600 transition-colors flex-shrink-0"
          title="Logout"
        >
          <svg
            class="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
            />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";

// Props
const props = defineProps({
  leadCount: { type: Number, default: 0 },
  userName: { type: String, default: "John Doe" },
  userEmail: { type: String, default: "<EMAIL>" },
  isMobileMenuOpen: { type: Boolean, default: false },
});

// Emits
const emit = defineEmits(["logout", "toggle-mobile-menu"]);

const route = useRoute();
const router = useRouter();

// Menu items
const menuItems = [
  {
    name: "dashboard",
    label: "Dashboard",
    to: "/dashboard",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v14l-5-3-5 3V5z"/></svg>`,
    },
  },
  {
    name: "leads",
    label: "Leads",
    to: "/leads",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/></svg>`,
    },
  },
  {
    name: "campaigns",
    label: "Campaigns",
    to: "/campaigns",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"/></svg>`,
    },
  },
  {
    name: "analytics",
    label: "Analytics",
    to: "/analytics",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/></svg>`,
    },
  },
  {
    name: "integrations",
    label: "Integrations",
    to: "/integrations",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/></svg>`,
    },
  },
];

const bottomItems = [
  {
    name: "settings",
    label: "Settings",
    to: "/settings",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/></svg>`,
    },
  },
  {
    name: "help",
    label: "Help & Support",
    to: "/help",
    icon: {
      template: `<svg fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/></svg>`,
    },
  },
];

// User initials
const userInitials = computed(() => {
  return props.userName
    .split(" ")
    .map((n) => n.charAt(0).toUpperCase())
    .join("")
    .substring(0, 2);
});

// Active state checker
const isActive = (path) => {
  return route.path.startsWith(path);
};

// Logout
const handleLogout = () => {
  emit("logout");
  closeMobileMenu();
};

// Close mobile menu
const closeMobileMenu = () => {
  emit("toggle-mobile-menu", false);
};
</script>
