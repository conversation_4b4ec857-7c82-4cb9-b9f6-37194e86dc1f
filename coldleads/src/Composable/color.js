
export const colors = {
  // Primary brand colors (Deep Emerald - Professional & Unique)
  primary: {
    50: '#ecfdf5',
    100: '#d1fae5', 
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Main primary - Deep Emerald
    600: '#059669',
    700: '#047857',
    800: '#065f46',
    900: '#064e3b'
  },

  // Secondary brand colors (Deep Purple - Premium feel)
  secondary: {
    50: '#faf5ff',
    100: '#f3e8ff',
    200: '#e9d5ff',
    300: '#d8b4fe',
    400: '#c084fc',
    500: '#a855f7', // Main secondary - Deep Purple
    600: '#9333ea',
    700: '#7c3aed',
    800: '#6b21a8',
    900: '#581c87'
  },

  // Neutral grays (Warm tone)
  neutral: {
    50: '#fafaf9',  // Very light background
    100: '#f5f5f4', // Light background
    200: '#e7e5e4', // Border light
    300: '#d6d3d1', // Border default
    400: '#a8a29e', // Text muted
    500: '#78716c', // Text secondary
    600: '#57534e', // Text primary light
    700: '#44403c', // Text primary
    800: '#292524', // Text primary dark
    900: '#1c1917'  // Text primary darkest
  },

  // Success colors (Emerald)
  success: {
    50: '#ecfdf5',
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    400: '#34d399',
    500: '#10b981', // Main success
    600: '#059669',
    700: '#047857'
  },

  // Warning colors (Amber)
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b', // Main warning
    600: '#d97706',
    700: '#b45309'
  },

  // Error colors (Rose)
  error: {
    50: '#fff1f2',
    100: '#ffe4e6',
    200: '#fecdd3',
    300: '#fda4af',
    400: '#fb7185',
    500: '#f43f5e', // Main error
    600: '#e11d48',
    700: '#be123c'
  },

  // Accent colors
  indigo: {
    500: '#6366f1',
    600: '#4f46e5'
  },

  // Orange accent (for automation)
  orange: {
    500: '#f97316',
    600: '#ea580c'
  }
}

// Semantic color mappings for easier use
export const semanticColors = {
  // Brand colors
  brand: colors.primary[500], // Emerald
  brandLight: colors.primary[100],
  brandDark: colors.primary[700],
  
  // Secondary brand
  accent: colors.secondary[500], // Purple
  accentLight: colors.secondary[100],
  accentDark: colors.secondary[700],
  
  // Backgrounds
  background: '#ffffff',
  backgroundMuted: colors.neutral[50],
  backgroundCard: '#ffffff',
  
  // Text colors
  textPrimary: colors.neutral[900],
  textSecondary: colors.neutral[600],
  textMuted: colors.neutral[400],
  textInverse: '#ffffff',
  
  // Status colors
  success: colors.success[500],
  warning: colors.warning[500],
  error: colors.error[500],
  
  // UI elements
  border: colors.neutral[200],
  borderLight: colors.neutral[100],
  borderDark: colors.neutral[300],
  
  // Interactive elements
  hover: colors.neutral[50],
  focus: colors.primary[500],
  disabled: colors.neutral[300]
}

// Two-tone color combinations for different UI states
export const twoToneColors = {
  // Primary combinations (Emerald)
  primaryLight: {
    bg: colors.primary[50],
    text: colors.primary[700],
    border: colors.primary[200]
  },
  
  // Secondary combinations (Purple)
  secondaryLight: {
    bg: colors.secondary[50],
    text: colors.secondary[700],
    border: colors.secondary[200]
  },
  
  // Success combinations  
  successLight: {
    bg: colors.success[50],
    text: colors.success[700],
    border: colors.success[200]
  },
  
  // Warning combinations
  warningLight: {
    bg: colors.warning[50],
    text: colors.warning[700],
    border: colors.warning[200]
  },
  
  // Error combinations
  errorLight: {
    bg: colors.error[50],
    text: colors.error[700],
    border: colors.error[200]
  },
  
  // Neutral combinations
  neutralLight: {
    bg: colors.neutral[50],
    text: colors.neutral[700],
    border: colors.neutral[200]
  }
}

// CSS custom properties for use in components
export const cssVariables = `
  --color-primary-50: ${colors.primary[50]};
  --color-primary-500: ${colors.primary[500]};
  --color-primary-600: ${colors.primary[600]};
  --color-primary-700: ${colors.primary[700]};
  
  --color-secondary-50: ${colors.secondary[50]};
  --color-secondary-500: ${colors.secondary[500]};
  --color-secondary-600: ${colors.secondary[600]};
  --color-secondary-700: ${colors.secondary[700]};
  
  --color-neutral-50: ${colors.neutral[50]};
  --color-neutral-200: ${colors.neutral[200]};
  --color-neutral-300: ${colors.neutral[300]};
  --color-neutral-400: ${colors.neutral[400]};
  --color-neutral-600: ${colors.neutral[600]};
  --color-neutral-700: ${colors.neutral[700]};
  --color-neutral-900: ${colors.neutral[900]};
  
  --color-success: ${colors.success[500]};
  --color-warning: ${colors.warning[500]};
  --color-error: ${colors.error[500]};
  
  --color-background: #ffffff;
  --color-background-muted: ${colors.neutral[50]};
`