import { jwtDecode } from "jwt-decode";


const tokenKey = 'token';
const userKey = 'user';

export function getToken() {
  return localStorage.getItem(tokenKey);
}

export function getUser() {
  const userStr = localStorage.getItem(userKey);
  return userStr ? JSON.parse(userStr) : null;
}


export function isLoggedIn() {
  const token = getToken();
  if (!token) return false;

  try {
    const decoded = jwtDecode(token);
    const now = Date.now() / 1000;
    return decoded.exp > now;
  } catch (err) {
    return false;
  }
}

export function logout() {
  localStorage.removeItem(tokenKey);
  localStorage.removeItem(userKey);
}